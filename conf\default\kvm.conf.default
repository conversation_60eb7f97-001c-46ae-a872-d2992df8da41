[kvm]
# Specify a comma-separated list of available machines to be used. For each
# specified ID you have to define a dedicated section containing the details
# on the respective machine. (E.g. cuckoo1,cuckoo2,cuckoo3)
machines = cuckoo1

# Interface bridge cho KVM network - có thể là virbr0 hoặc br0
interface = virbr0
# To connect to local or remote host
dsn = qemu:///system

# To allow copy & paste. For details see example below
[cape1]
label = cape1
platform = windows
ip = ***************
arch = x86
# tags = winxp,acrobat_reader_6
# snapshot = Snapshot1
# resultserver_ip = ***************
# reserved = no

[cuckoo1]
# Specify the label name of the current machine as specified in your
# libvirt configuration.
label = cuckoo1

# Specify the operating system platform used by current machine
# [windows/darwin/linux].
platform = windows

# Specify the IP address of the current virtual machine. Make sure that the
# IP address is valid and that the host machine is able to reach it. If not,
# the analysis will fail. You may want to configure your network settings in
# /etc/libvirt/<hypervisor>/networks/
# Sử dụng dải IP 172.16.11.x cho VM
ip = *************

# Specify tags to display
# Tags may be used to specify on which guest machines a sample should be run
# NOTE - One of the following OS version tags MUST be included for Windows VMs:
# winxp,win7, win10, win11
# Some samples will only detonate on specific versions of Windows (see web.conf packages for more info)
# Example: MSIX - Windows >= 10
# tags = winxp,acrobat_reader_6

# (Optional) Specify the snapshot name to use.
# If empty, it will use the current/latest snapshot.
# Example (Snapshot1 is the snapshot name):
# snapshot = Snapshot1

# (Optional) Specify the name of the network interface that should be used
# when dumping network traffic from this machine with tcpdump. If specified,
# overrides the default interface specified in auxiliary.conf
# Example (virbr0 is the interface name):
# interface = virbr0

# (Optional) Specify the IP of the Result Server, as your virtual machine sees it.
# The Result Server will always bind to the address and port specified in cuckoo.conf,
# however you could set up your virtual network to use NAT/PAT, so you can specify here
# the IP address for the Result Server as your machine sees it. If you don't specify an
# address here, the machine will use the default value from cuckoo.conf.
# NOTE: if you set this option you have to set result server IP to 0.0.0.0 in cuckoo.conf.
# Example:
resultserver_ip = ************

# (Optional) Specify the port for the Result Server, as your virtual machine sees it.
# The Result Server will always bind to the address and port specified in cuckoo.conf,
# however you could set up your virtual network to use NAT/PAT, so you can specify here
# the port for the Result Server as your machine sees it. If you don't specify a port
# here, the machine will use the default value from cuckoo.conf.
# Example:
# resultserver_port = 2042

# Set the machine architecture
# Required to auto select proper machine architecture for sample
# x64 or x86
arch = x86

# (Optional) Specify whether or not the machine should be reserved, meaning that it will
# only be used for a detonation if specifically requested by its label.
# reserved = no
