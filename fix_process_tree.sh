#!/bin/bash

# Script kiểm tra và sửa lỗi Process Tree trong CAPEv2
# Sử dụng: ./fix_process_tree.sh [task_id]

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

function print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

function print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

function print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

function print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Tìm CAPE path
CAPE_PATH="/opt/CAPEv2"
if [ -d "/mnt/CAPEv2" ]; then
    CAPE_PATH="/mnt/CAPEv2"
fi

TASK_ID="$1"

print_info "=== KIỂM TRA VÀ SỬA LỖI PROCESS TREE ==="
print_info "CAPE Path: $CAPE_PATH"

print_step "1. Kiểm tra cấu hình processing"

# Kiểm tra processing.conf
PROCESSING_CONF="$CAPE_PATH/conf/processing.conf"
if [ -f "$PROCESSING_CONF" ]; then
    print_info "Kiểm tra processing.conf..."
    
    # Kiểm tra behavior module có được enable không
    if grep -q "^\[behavior\]" "$PROCESSING_CONF"; then
        BEHAVIOR_ENABLED=$(grep -A5 "^\[behavior\]" "$PROCESSING_CONF" | grep "^enabled" | cut -d'=' -f2 | tr -d ' ')
        if [ "$BEHAVIOR_ENABLED" = "yes" ] || [ "$BEHAVIOR_ENABLED" = "true" ] || [ "$BEHAVIOR_ENABLED" = "1" ]; then
            print_info "✅ Behavior processing enabled"
        else
            print_error "❌ Behavior processing disabled"
            print_info "Enabling behavior processing..."
            sed -i '/^\[behavior\]/,/^\[/ s/^enabled.*/enabled = yes/' "$PROCESSING_CONF"
        fi
    else
        print_error "❌ [behavior] section not found in processing.conf"
    fi
    
    # Kiểm tra processtree có được enable không
    if grep -q "processtree" "$PROCESSING_CONF"; then
        PROCESSTREE_ENABLED=$(grep "processtree" "$PROCESSING_CONF" | cut -d'=' -f2 | tr -d ' ')
        if [ "$PROCESSTREE_ENABLED" = "yes" ] || [ "$PROCESSTREE_ENABLED" = "true" ] || [ "$PROCESSTREE_ENABLED" = "1" ]; then
            print_info "✅ ProcessTree enabled"
        else
            print_error "❌ ProcessTree disabled"
            print_info "Enabling ProcessTree..."
            sed -i 's/processtree.*/processtree = yes/' "$PROCESSING_CONF"
        fi
    else
        print_warning "⚠️  ProcessTree option not found, adding..."
        sed -i '/^\[behavior\]/a processtree = yes' "$PROCESSING_CONF"
    fi
else
    print_error "❌ processing.conf not found at $PROCESSING_CONF"
fi

print_step "2. Kiểm tra behavior.py module"

BEHAVIOR_PY="$CAPE_PATH/modules/processing/behavior.py"
if [ -f "$BEHAVIOR_PY" ]; then
    print_info "✅ behavior.py exists"
    
    # Kiểm tra ProcessTree class
    if grep -q "class ProcessTree" "$BEHAVIOR_PY"; then
        print_info "✅ ProcessTree class found"
    else
        print_error "❌ ProcessTree class not found"
    fi
    
    # Kiểm tra ProcessTree được instantiate không
    if grep -q "ProcessTree()" "$BEHAVIOR_PY"; then
        print_info "✅ ProcessTree instantiated"
    else
        print_error "❌ ProcessTree not instantiated"
    fi
else
    print_error "❌ behavior.py not found at $BEHAVIOR_PY"
fi

print_step "3. Kiểm tra logs và analysis results"

if [ -n "$TASK_ID" ]; then
    ANALYSIS_PATH="$CAPE_PATH/storage/analyses/$TASK_ID"
    LOGS_PATH="$ANALYSIS_PATH/logs"
    REPORTS_PATH="$ANALYSIS_PATH/reports"
    
    print_info "Kiểm tra analysis $TASK_ID..."
    
    if [ -d "$ANALYSIS_PATH" ]; then
        print_info "✅ Analysis directory exists: $ANALYSIS_PATH"
        
        # Kiểm tra logs
        if [ -d "$LOGS_PATH" ] && [ "$(ls -A $LOGS_PATH 2>/dev/null)" ]; then
            print_info "✅ Logs directory has files"
            LOG_COUNT=$(find "$LOGS_PATH" -name "*.bson" | wc -l)
            print_info "BSON log files: $LOG_COUNT"
        else
            print_error "❌ Logs directory empty or missing"
            print_error "Không có behavioral logs -> không thể tạo process tree"
        fi
        
        # Kiểm tra report.json
        REPORT_JSON="$REPORTS_PATH/report.json"
        if [ -f "$REPORT_JSON" ]; then
            print_info "✅ report.json exists"
            
            # Kiểm tra behavior section
            if jq -e '.behavior' "$REPORT_JSON" >/dev/null 2>&1; then
                print_info "✅ Behavior section exists in report"
                
                # Kiểm tra processes
                PROCESS_COUNT=$(jq '.behavior.processes | length' "$REPORT_JSON" 2>/dev/null || echo "0")
                print_info "Processes found: $PROCESS_COUNT"
                
                # Kiểm tra processtree
                if jq -e '.behavior.processtree' "$REPORT_JSON" >/dev/null 2>&1; then
                    TREE_COUNT=$(jq '.behavior.processtree | length' "$REPORT_JSON" 2>/dev/null || echo "0")
                    if [ "$TREE_COUNT" -gt 0 ]; then
                        print_info "✅ Process tree exists with $TREE_COUNT nodes"
                    else
                        print_error "❌ Process tree empty"
                    fi
                else
                    print_error "❌ Process tree not found in report"
                fi
            else
                print_error "❌ No behavior section in report"
            fi
        else
            print_error "❌ report.json not found"
        fi
    else
        print_error "❌ Analysis directory not found: $ANALYSIS_PATH"
    fi
fi

print_step "4. Kiểm tra auxiliary modules"

# Kiểm tra các auxiliary modules quan trọng
AUX_MODULES=(
    "procmon"
    "human"
    "screenshots"
)

for module in "${AUX_MODULES[@]}"; do
    AUX_FILE="$CAPE_PATH/analyzer/windows/modules/auxiliary/$module.py"
    if [ -f "$AUX_FILE" ]; then
        print_info "✅ $module.py exists"
    else
        print_warning "⚠️  $module.py not found"
    fi
done

print_step "5. Kiểm tra monitor injection"

# Kiểm tra monitor DLL
MONITOR_DLL="$CAPE_PATH/analyzer/windows/dll/monitor.dll"
if [ -f "$MONITOR_DLL" ]; then
    print_info "✅ monitor.dll exists"
else
    print_error "❌ monitor.dll not found"
    print_error "Monitor DLL cần thiết để capture API calls"
fi

print_step "6. Kiểm tra logs chi tiết"

if [ -n "$TASK_ID" ] && [ -d "$LOGS_PATH" ]; then
    print_info "Phân tích logs chi tiết..."
    
    # Kiểm tra từng log file
    for logfile in "$LOGS_PATH"/*.bson; do
        if [ -f "$logfile" ]; then
            filename=$(basename "$logfile")
            print_info "Log file: $filename"
            
            # Kiểm tra size
            size=$(stat -f%z "$logfile" 2>/dev/null || stat -c%s "$logfile" 2>/dev/null || echo "0")
            if [ "$size" -gt 0 ]; then
                print_info "  Size: $size bytes"
            else
                print_warning "  Empty log file"
            fi
        fi
    done
fi

print_step "7. Tạo script test process tree"

cat > "$CAPE_PATH/test_process_tree.py" << 'EOF'
#!/usr/bin/env python3
"""
Script test process tree generation
"""
import sys
import os
import json
from pathlib import Path

# Add CAPE modules to path
sys.path.insert(0, '/opt/CAPEv2')
if os.path.exists('/mnt/CAPEv2'):
    sys.path.insert(0, '/mnt/CAPEv2')

try:
    from modules.processing.behavior import ProcessTree, Processes
    from lib.cuckoo.common.config import Config
    from lib.cuckoo.common.objects import Dictionary
    
    def test_process_tree(task_id):
        """Test process tree generation for a specific task"""
        cape_path = '/mnt/CAPEv2' if os.path.exists('/mnt/CAPEv2') else '/opt/CAPEv2'
        
        analysis_path = f"{cape_path}/storage/analyses/{task_id}"
        logs_path = f"{analysis_path}/logs"
        
        if not os.path.exists(logs_path):
            print(f"❌ Logs path not found: {logs_path}")
            return False
            
        print(f"✅ Testing process tree for task {task_id}")
        print(f"Logs path: {logs_path}")
        
        # Load config
        config = Config()
        options = Dictionary()
        options.analysis_call_limit = 100000
        
        # Create task object
        task = {"id": task_id}
        
        try:
            # Process logs
            print("📊 Processing behavioral logs...")
            processes_obj = Processes(logs_path, task, options)
            processes = processes_obj.run()
            
            print(f"Found {len(processes)} processes")
            
            if not processes:
                print("❌ No processes found in logs")
                return False
                
            # Create process tree
            print("🌳 Creating process tree...")
            tree_obj = ProcessTree()
            tree_obj.processes = []
            
            # Convert processes to tree format
            for proc in processes:
                tree_proc = {
                    "pid": proc["process_id"],
                    "parent_id": proc["parent_id"],
                    "name": proc["process_name"],
                    "children": []
                }
                tree_obj.processes.append(tree_proc)
            
            # Generate tree
            tree = tree_obj.run()
            
            print(f"✅ Process tree generated with {len(tree)} root nodes")
            
            # Print tree structure
            def print_tree(nodes, indent=0):
                for node in nodes:
                    print("  " * indent + f"├─ {node['name']} (PID: {node['pid']})")
                    if node['children']:
                        print_tree(node['children'], indent + 1)
            
            print("\n🌳 Process Tree Structure:")
            print_tree(tree)
            
            return True
            
        except Exception as e:
            print(f"❌ Error processing: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
    
    if __name__ == "__main__":
        if len(sys.argv) != 2:
            print("Usage: python3 test_process_tree.py <task_id>")
            sys.exit(1)
            
        task_id = sys.argv[1]
        success = test_process_tree(task_id)
        sys.exit(0 if success else 1)
        
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Kiểm tra CAPE installation và Python path")
    sys.exit(1)
EOF

chmod +x "$CAPE_PATH/test_process_tree.py"

print_info "=== TỔNG KẾT ==="
print_info ""
print_info "📋 CÁC BƯỚC KIỂM TRA TIẾP THEO:"
print_info ""
print_info "1. Kiểm tra cấu hình:"
print_info "   nano $CAPE_PATH/conf/processing.conf"
print_info "   # Đảm bảo [behavior] enabled = yes"
print_info "   # Đảm bảo processtree = yes"
print_info ""
print_info "2. Test với task cụ thể:"
if [ -n "$TASK_ID" ]; then
    print_info "   cd $CAPE_PATH && python3 test_process_tree.py $TASK_ID"
else
    print_info "   cd $CAPE_PATH && python3 test_process_tree.py <task_id>"
fi
print_info ""
print_info "3. Kiểm tra logs:"
print_info "   tail -f $CAPE_PATH/logs/cuckoo.log | grep -i 'process\\|tree\\|behavior'"
print_info ""
print_info "4. Reprocess analysis:"
print_info "   cd $CAPE_PATH && python3 utils/process.py -r <task_id>"
print_info ""
print_warning "⚠️  NGUYÊN NHÂN THƯỜNG GẶP:"
print_warning "- Behavior processing bị disable"
print_warning "- Không có behavioral logs (monitor injection failed)"
print_warning "- Lỗi trong ProcessTree class"
print_warning "- VM không có network/internet để download monitor"
print_warning "- Antivirus block monitor injection"
